# SITP-TCP Bridge and Proxy Makefile

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -O2 -DLOG_USE_COLOR
INCLUDES = -Iinclude -Ilib/sitp -Ilib/log -Ilib/ini -Ilib/ut
LIBS = -levent -lpthread

BUILD_DIR = build
BIN_DIR = bin
LIB_BUILD_DIR = $(BUILD_DIR)/lib
COMMON_BUILD_DIR = $(BUILD_DIR)/common
BRIDGE_BUILD_DIR = $(BUILD_DIR)/bridge
PROXY_BUILD_DIR = $(BUILD_DIR)/proxy

LIB_SOURCES = lib/log/log.c lib/ini/ini.c lib/sitp/sitp_lib.c
COMMON_SOURCES = $(wildcard src/common/*.c)
BRIDGE_SOURCES = $(wildcard src/bridge/*.c)
PROXY_SOURCES = $(wildcard src/proxy/*.c)

LIB_OBJECTS = $(patsubst lib/%.c,$(LIB_BUILD_DIR)/%.o,$(LIB_SOURCES))
COMMON_OBJECTS = $(patsubst src/common/%.c,$(COMMON_BUILD_DIR)/%.o,$(COMMON_SOURCES))
BRIDGE_OBJECTS = $(patsubst src/bridge/%.c,$(BRIDGE_BUILD_DIR)/%.o,$(BRIDGE_SOURCES))
PROXY_OBJECTS = $(patsubst src/proxy/%.c,$(PROXY_BUILD_DIR)/%.o,$(PROXY_SOURCES))

BRIDGE_TARGET = $(BIN_DIR)/tcp_bridge
PROXY_TARGET = $(BIN_DIR)/tcp_proxy
DEMO_TARGET = $(BIN_DIR)/demo
BRIDGE_MAIN_OBJ = $(BRIDGE_BUILD_DIR)/tcp_bridge.o
PROXY_MAIN_OBJ = $(PROXY_BUILD_DIR)/tcp_proxy.o

.PHONY: all clean bridge proxy demo install uninstall test-colors

all: bridge proxy

bridge: $(BRIDGE_TARGET) $(BIN_DIR)/bridge.conf

proxy: $(PROXY_TARGET) $(BIN_DIR)/proxy.conf

demo: $(DEMO_TARGET)

$(BRIDGE_TARGET): $(LIB_OBJECTS) $(COMMON_OBJECTS) $(filter-out $(BRIDGE_MAIN_OBJ), $(BRIDGE_OBJECTS)) $(BRIDGE_MAIN_OBJ) | $(BIN_DIR)
	$(CC) -o $@ $^ $(LIBS)

$(PROXY_TARGET): $(LIB_OBJECTS) $(COMMON_OBJECTS) $(filter-out $(PROXY_MAIN_OBJ), $(PROXY_OBJECTS)) $(PROXY_MAIN_OBJ) | $(BIN_DIR)
	$(CC) -o $@ $^ $(LIBS)

$(DEMO_TARGET): $(LIB_OBJECTS) $(COMMON_OBJECTS) src/demo/demo.c | $(BIN_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)

$(BIN_DIR)/bridge.conf: bridge.conf | $(BIN_DIR)
	cp bridge.conf $(BIN_DIR)/

$(BIN_DIR)/proxy.conf: proxy.conf | $(BIN_DIR)
	cp proxy.conf $(BIN_DIR)/

$(LIB_BUILD_DIR)/%.o: lib/%.c | $(LIB_BUILD_DIR)
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(COMMON_BUILD_DIR)/%.o: src/common/%.c | $(COMMON_BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(BRIDGE_BUILD_DIR)/%.o: src/bridge/%.c | $(BRIDGE_BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(PROXY_BUILD_DIR)/%.o: src/proxy/%.c | $(PROXY_BUILD_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(LIB_BUILD_DIR):
	mkdir -p $(LIB_BUILD_DIR)/log $(LIB_BUILD_DIR)/ini $(LIB_BUILD_DIR)/sitp

$(COMMON_BUILD_DIR):
	mkdir -p $(COMMON_BUILD_DIR)

$(BRIDGE_BUILD_DIR):
	mkdir -p $(BRIDGE_BUILD_DIR)

$(PROXY_BUILD_DIR):
	mkdir -p $(PROXY_BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

clean:
	rm -rf $(BUILD_DIR) $(BIN_DIR)

install: $(BRIDGE_TARGET) $(PROXY_TARGET)
	mkdir -p /usr/local/bin
	cp $(BRIDGE_TARGET) /usr/local/bin/
	cp $(PROXY_TARGET) /usr/local/bin/
	chmod +x /usr/local/bin/tcp_bridge
	chmod +x /usr/local/bin/tcp_proxy

uninstall:
	rm -f /usr/local/bin/tcp_bridge
	rm -f /usr/local/bin/tcp_proxy

debug: CFLAGS += -DDEBUG -ggdb
debug: $(BRIDGE_TARGET) $(PROXY_TARGET) $(BIN_DIR)/bridge.conf $(BIN_DIR)/proxy.conf

release: CFLAGS += -DNDEBUG -O3
release: $(BRIDGE_TARGET) $(PROXY_TARGET) $(BIN_DIR)/bridge.conf $(BIN_DIR)/proxy.conf

deps:
	@echo "Required dependencies:"
	@echo "  - libevent-dev"
	@echo "  - build-essential"
	@echo ""
	@echo "Install on Ubuntu/Debian:"
	@echo "  sudo apt-get install libevent-dev build-essential"
	@echo ""
	@echo "Install on CentOS/RHEL:"
	@echo "  sudo yum install libevent-devel gcc make"

help:
	@echo "Available targets:"
	@echo "  all       - Build all targets (default)"
	@echo "  bridge    - Build TCP bridge service"
	@echo "  proxy     - Build TCP proxy service"
	@echo "  clean     - Clean all build artifacts"
	@echo "  install   - Install binaries to /usr/local/bin"
	@echo "  uninstall - Remove installed binaries"
	@echo "  debug     - Build with debug symbols"
	@echo "  release   - Build optimized release version"
	@echo "  test-colors - Test colored log output"
	@echo "  deps      - Show required dependencies"
	@echo "  help      - Show this help message"

test-colors: $(BRIDGE_TARGET) $(PROXY_TARGET)
	@echo "Testing colored log output..."
	@echo "Bridge service:"
	-@timeout 2s $(BRIDGE_TARGET) -c /nonexistent.conf 2>&1 || true
	@echo ""
	@echo "Proxy service:"
	-@timeout 2s $(PROXY_TARGET) -c /nonexistent.conf 2>&1 || true
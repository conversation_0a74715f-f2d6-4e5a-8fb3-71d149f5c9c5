#include "../../include/common/hex_utils.h"
#include "../../lib/log/log.h"
#include "../../lib/sitp/sitp_lib.h"
#include <getopt.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static volatile int running = 1;
static void *sitp_handle = NULL;
static int verbose_mode = 0;

void recv_callback(void *arg, uint8_t *buffer, size_t len) {
  uint16_t *board_ids = (uint16_t *)arg;
  printf("[Board 0x%04X] Received %zu bytes from 0x%04X\n", board_ids[0], len,
         board_ids[1]);

  if (verbose_mode) {
    print_hexdump(buffer, len);
  }
}

void *sitp_thread_func(void *arg) {
  sitp_lib_start();
  return NULL;
}

void signal_handler(int sig) {
  printf("\nShutting down...\n");
  running = 0;
  exit(0);
}

int main(int argc, char *argv[]) {
  int opt;
  uint16_t local_board_id, remote_board_id;

  while ((opt = getopt(argc, argv, "v")) != -1) {
    switch (opt) {
    case 'v':
      verbose_mode = 1;
      break;
    default:
      printf("Usage: %s [-v] <local_board_id> <remote_board_id>\n", argv[0]);
      printf("  -v: Enable verbose mode (hex dump of data)\n");
      printf("Example: %s -v 0xff10 0xff12\n", argv[0]);
      return 1;
    }
  }

  if (argc - optind != 2) {
    printf("Usage: %s [-v] <local_board_id> <remote_board_id>\n", argv[0]);
    printf("  -v: Enable verbose mode (hex dump of data)\n");
    printf("Example: %s -v 0xff10 0xff12\n", argv[0]);
    return 1;
  }

  local_board_id = (uint16_t)strtol(argv[optind], NULL, 16);
  remote_board_id = (uint16_t)strtol(argv[optind + 1], NULL, 16);
  uint16_t board_ids[2] = {local_board_id, remote_board_id};

  printf("Starting SITP demo:\n");
  printf("  Local Board ID:  0x%04X\n", local_board_id);
  printf("  Remote Board ID: 0x%04X\n", remote_board_id);

  signal(SIGINT, signal_handler);
  signal(SIGTERM, signal_handler);

  log_set_level(LOG_INFO);

  sitp_handle = sitp_lib_add("eth0", 1500, 0x8888, local_board_id,
                             remote_board_id, 0, 0, recv_callback, board_ids);

  if (!sitp_handle) {
    printf("Failed to initialize SITP interface\n");
    return 1;
  }

  pthread_t sitp_thread;
  if (pthread_create(&sitp_thread, NULL, sitp_thread_func, NULL) != 0) {
    printf("Failed to create SITP thread\n");
    return 1;
  }

  printf("SITP demo started. Type messages to send (or 'quit' to exit):\n");

  char input[256];
  while (running && fgets(input, sizeof(input), stdin)) {
    input[strcspn(input, "\n")] = 0;

    if (strcmp(input, "quit") == 0) {
      break;
    }

    if (strlen(input) > 0) {
      int result = sitp_lib_send(sitp_handle, (uint8_t *)input, strlen(input));
      if (result == 0) {
        printf("[Board 0x%04X] Successfully sent %zu bytes to 0x%04X\n",
               local_board_id, strlen(input), remote_board_id);

        if (verbose_mode) {
          print_hexdump((const unsigned char *)input, strlen(input));
        }
      } else {
        printf("[Board 0x%04X] Failed to send data to 0x%04X\n", local_board_id,
               remote_board_id);
      }
    }
  }

  running = 0;
  pthread_join(sitp_thread, NULL);

  printf("Demo terminated\n");
  return 0;
}

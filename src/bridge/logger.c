#include "bridge/logger.h"
#include "log.h"
#include <stdio.h>

static FILE *log_fp = NULL;

int bridge_logger_init(const bridge_config_t *config) {
  log_set_level(config->log_level);

  if (config->log_to_file) {
    log_fp = fopen(config->log_filename, "a");
    if (!log_fp) {
      log_error("Failed to open log file: %s", config->log_filename);
      return -1;
    }
    log_add_fp(log_fp, config->log_level);
    log_info("Logging to file: %s", config->log_filename);
  }

  log_info("Logger initialized with level: %d", config->log_level);
  return 0;
}

void bridge_logger_cleanup(void) {
  if (log_fp) {
    fclose(log_fp);
    log_fp = NULL;
  }
}

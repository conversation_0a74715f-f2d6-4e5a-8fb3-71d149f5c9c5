#include "bridge/sitp_client.h"
#include "common/protocol.h"
#include "log.h"
#include "sitp_lib.h"
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static void sitp_recv_callback(void *arg, uint8_t *buffer, size_t len);
static void *sitp_thread_func(void *arg);
static void process_complete_message(const uint8_t *message_data, size_t message_len, void *arg);

int sitp_client_init(sitp_client_t *client, bridge_config_t *config,
                     bridge_pipes_t *pipes) {
  memset(client, 0, sizeof(sitp_client_t));
  client->config = config;
  client->pipes = pipes;
  client->running = 0;

  // Create message buffer for handling fragmented/concatenated messages
  client->recv_buffer = message_buffer_create(8192);
  if (!client->recv_buffer) {
    log_error("Failed to create message buffer");
    return -1;
  }

  client->sitp_handle =
      sitp_lib_add(config->sitp_interface, config->sitp_mtu,
                   config->sitp_protocol, config->sitp_local_board_id,
                   config->sitp_remote_board_id, config->sitp_local_port,
                   config->sitp_remote_port, sitp_recv_callback, client);

  if (!client->sitp_handle) {
    log_error("Failed to add SITP interface");
    message_buffer_destroy(client->recv_buffer);
    return -1;
  }

  log_info("SITP client initialized:");
  log_info("  Interface    : %s", config->sitp_interface);
  log_info("  Protocol     : 0x%04x", config->sitp_protocol);
  log_info("  Local ID     : 0x%04x", config->sitp_local_board_id);
  log_info("  Remote ID    : 0x%04x", config->sitp_remote_board_id);
  log_info("  Local Port   : %d", config->sitp_local_port);
  log_info("  Remote Port  : %d", config->sitp_remote_port);
  log_info("  MTU Size     : %d", config->sitp_mtu);

  return 0;
}

int sitp_client_start(sitp_client_t *client) {
  client->running = 1;

  if (pthread_create(&client->sitp_thread, NULL, sitp_thread_func, client) !=
      0) {
    log_error("Failed to create SITP thread");
    client->running = 0;
    return -1;
  }

  log_info("SITP client started");
  return 0;
}

void sitp_client_stop(sitp_client_t *client) {
  if (client->running) {
    client->running = 0;

    if (pthread_join(client->sitp_thread, NULL) != 0) {
      log_warn("Failed to join SITP thread");
    }

    log_info("SITP client stopped");
  }
}

void sitp_client_cleanup(sitp_client_t *client) {
  sitp_client_stop(client);

  if (client->sitp_handle) {
    client->sitp_handle = NULL;
  }

  if (client->recv_buffer) {
    message_buffer_destroy(client->recv_buffer);
    client->recv_buffer = NULL;
  }

  log_info("SITP client cleaned up");
}

static void sitp_recv_callback(void *arg, uint8_t *buffer, size_t len) {
  sitp_client_t *client = (sitp_client_t *)arg;

  if (!client->running) {
    return;
  }

  log_debug("Received %zu bytes from SITP", len);

  // Append received data to buffer
  if (message_buffer_append(client->recv_buffer, buffer, len) < 0) {
    log_error("Failed to append data to message buffer");
    return;
  }

  // Process complete messages from buffer
  int processed = message_buffer_process_messages(client->recv_buffer,
                                                  client->config->padding_size,
                                                  process_complete_message,
                                                  client);

  if (processed < 0) {
    log_error("Failed to process messages from buffer");
  } else if (processed > 0) {
    log_debug("Processed %d complete messages", processed);
  }
}

static void process_complete_message(const uint8_t *message_data, size_t message_len, void *arg) {
  sitp_client_t *client = (sitp_client_t *)arg;

  if (!client->running) {
    return;
  }

  // Validate the complete message one more time
  if (!validate_padded_message(message_data, message_len, client->config->padding_size)) {
    log_warn("Invalid complete message in processor");
    return;
  }

  // Write the complete message to pipe
  if (bridge_pipes_write_sitp_to_tcp(client->pipes, message_data, message_len) < 0) {
    log_warn("Failed to write complete SITP message to queue: expected=%zu", message_len);
  } else {
    log_debug("Forwarded complete %zu bytes message from SITP to queue", message_len);
  }
}

static void *sitp_thread_func(void *arg) {
  sitp_client_t *client = (sitp_client_t *)arg;

  log_info("SITP thread started");
  sitp_lib_start();
  log_info("SITP thread exiting");

  return NULL;
}

#include "bridge/pipes.h"
#include "log.h"
#include <stdlib.h>

int bridge_pipes_init(bridge_pipes_t *pipes, struct event_base *base, int queue_capacity) {
  pipes->base = base;
  pipes->tcp_to_sitp_queue = NULL;
  pipes->sitp_to_tcp_queue = NULL;

  // Create TCP to SITP queue
  pipes->tcp_to_sitp_queue = queue_create(queue_capacity, base);
  if (!pipes->tcp_to_sitp_queue) {
    log_error("Failed to create tcp_to_sitp queue");
    return -1;
  }

  // Create SITP to TCP queue
  pipes->sitp_to_tcp_queue = queue_create(queue_capacity, base);
  if (!pipes->sitp_to_tcp_queue) {
    log_error("Failed to create sitp_to_tcp queue");
    queue_destroy(pipes->tcp_to_sitp_queue);
    pipes->tcp_to_sitp_queue = NULL;
    return -1;
  }

  log_info("Bridge pipes initialized successfully");
  return 0;
}

void bridge_pipes_cleanup(bridge_pipes_t *pipes) {
  if (!pipes) {
    return;
  }

  if (pipes->tcp_to_sitp_queue) {
    queue_destroy(pipes->tcp_to_sitp_queue);
    pipes->tcp_to_sitp_queue = NULL;
  }

  if (pipes->sitp_to_tcp_queue) {
    queue_destroy(pipes->sitp_to_tcp_queue);
    pipes->sitp_to_tcp_queue = NULL;
  }

  log_info("Bridge pipes cleaned up");
}

int bridge_pipes_set_callbacks(bridge_pipes_t *pipes,
                               pipe_data_callback_t tcp_to_sitp_cb,
                               pipe_data_callback_t sitp_to_tcp_cb,
                               void *tcp_to_sitp_arg, void *sitp_to_tcp_arg) {
  if (!pipes || !tcp_to_sitp_cb || !sitp_to_tcp_cb) {
    log_error("Invalid parameters for setting pipe callbacks");
    return -1;
  }

  // Set callback for TCP to SITP queue
  if (queue_set_callback(pipes->tcp_to_sitp_queue, tcp_to_sitp_cb, tcp_to_sitp_arg) < 0) {
    log_error("Failed to set tcp_to_sitp queue callback");
    return -1;
  }

  // Set callback for SITP to TCP queue
  if (queue_set_callback(pipes->sitp_to_tcp_queue, sitp_to_tcp_cb, sitp_to_tcp_arg) < 0) {
    log_error("Failed to set sitp_to_tcp queue callback");
    return -1;
  }

  log_info("Pipe callbacks set successfully");
  return 0;
}

int bridge_pipes_write_tcp_to_sitp(bridge_pipes_t *pipes, const uint8_t *data, size_t len) {
  if (!pipes || !data || len == 0) {
    return -1;
  }

  return queue_enqueue(pipes->tcp_to_sitp_queue, data, len);
}

int bridge_pipes_write_sitp_to_tcp(bridge_pipes_t *pipes, const uint8_t *data, size_t len) {
  if (!pipes || !data || len == 0) {
    return -1;
  }

  return queue_enqueue(pipes->sitp_to_tcp_queue, data, len);
}

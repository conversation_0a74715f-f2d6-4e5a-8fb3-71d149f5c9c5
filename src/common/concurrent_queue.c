#include "common/concurrent_queue.h"
#include "log.h"
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/eventfd.h>
#include <unistd.h>

queue_t *queue_create(size_t capacity, struct event_base *base) {
  if (capacity == 0) {
    return NULL;
  }

  queue_t *q = (queue_t *)malloc(sizeof(queue_t));
  if (!q) {
    return NULL;
  }

  q->elements = (queue_element_t *)malloc(capacity * sizeof(queue_element_t));
  if (!q->elements) {
    free(q);
    return NULL;
  }

  q->capacity = capacity;
  q->count = 0;
  q->head = 0;
  q->tail = 0;
  q->base = base;
  q->event = NULL;
  q->closed = 0;

  // Create eventfd for libevent integration
  q->eventfd = eventfd(0, EFD_NONBLOCK | EFD_CLOEXEC);
  if (q->eventfd == -1) {
    log_error("Failed to create eventfd: %s", strerror(errno));
    free(q->elements);
    free(q);
    return NULL;
  }

  if (pthread_mutex_init(&q->mutex, NULL) != 0) {
    close(q->eventfd);
    free(q->elements);
    free(q);
    return NULL;
  }
  if (pthread_cond_init(&q->not_empty, NULL) != 0) {
    pthread_mutex_destroy(&q->mutex);
    close(q->eventfd);
    free(q->elements);
    free(q);
    return NULL;
  }
  if (pthread_cond_init(&q->not_full, NULL) != 0) {
    pthread_cond_destroy(&q->not_empty);
    pthread_mutex_destroy(&q->mutex);
    close(q->eventfd);
    free(q->elements);
    free(q);
    return NULL;
  }

  return q;
}

int queue_set_callback(queue_t *q, queue_callback_t callback, void *arg) {
  if (!q || !callback || !q->base) {
    return -1;
  }

  // Create libevent event for the eventfd
  q->event = event_new(q->base, q->eventfd, EV_READ | EV_PERSIST, callback, arg);
  if (!q->event) {
    log_error("Failed to create libevent event for queue");
    return -1;
  }

  // Add event to the event loop
  if (event_add(q->event, NULL) < 0) {
    log_error("Failed to add queue event to event loop");
    event_free(q->event);
    q->event = NULL;
    return -1;
  }

  log_debug("Queue callback set successfully");
  return 0;
}

void queue_destroy(queue_t *q) {
  if (!q) {
    return;
  }

  // Clean up libevent resources
  if (q->event) {
    event_free(q->event);
    q->event = NULL;
  }

  // Close eventfd
  if (q->eventfd >= 0) {
    close(q->eventfd);
    q->eventfd = -1;
  }

  pthread_mutex_lock(&q->mutex);
  // Free any remaining buffers in the queue
  for (size_t i = 0; i < q->count; ++i) {
    size_t current_index = (q->head + i) % q->capacity;
    if (q->elements[current_index].buffer) {
      free(q->elements[current_index].buffer);
      q->elements[current_index].buffer = NULL;
    }
  }
  pthread_mutex_unlock(&q->mutex);

  pthread_cond_destroy(&q->not_full);
  pthread_cond_destroy(&q->not_empty);
  pthread_mutex_destroy(&q->mutex);

  free(q->elements);
  free(q);
}

int queue_enqueue(queue_t *q, const uint8_t *buffer, size_t len) {
  if (!q || !buffer || len == 0 || q->closed) {
    return -1;
  }

  pthread_mutex_lock(&q->mutex);

  // For non-blocking behavior, return error if queue is full
  if (q->count == q->capacity) {
    pthread_mutex_unlock(&q->mutex);
    return -1;
  }

  // Allocate memory for the new buffer and copy data
  uint8_t *new_buffer = (uint8_t *)malloc(len);
  if (!new_buffer) {
    pthread_mutex_unlock(&q->mutex);
    return -1;
  }
  memcpy(new_buffer, buffer, len);

  q->elements[q->tail].buffer = new_buffer;
  q->elements[q->tail].len = len;
  q->tail = (q->tail + 1) % q->capacity;
  q->count++;

  pthread_cond_signal(&q->not_empty);

  // Signal eventfd to notify libevent
  if (q->eventfd >= 0) {
    uint64_t value = 1;
    ssize_t result = write(q->eventfd, &value, sizeof(value));
    if (result != sizeof(value)) {
      log_debug("Failed to write to eventfd: %s", strerror(errno));
    }
  }

  pthread_mutex_unlock(&q->mutex);
  return 0;
}

int queue_dequeue(queue_t *q, uint8_t **buffer, size_t *len) {
  if (!q || !buffer || !len) {
    return -1;
  }

  pthread_mutex_lock(&q->mutex);

  // Non-blocking: return error if queue is empty
  if (q->count == 0) {
    pthread_mutex_unlock(&q->mutex);
    return -1;
  }

  *buffer = q->elements[q->head].buffer;
  *len = q->elements[q->head].len;

  // Mark the slot as "empty" for clarity, though not strictly necessary
  // as the buffer pointer is now owned by the caller.
  q->elements[q->head].buffer = NULL;
  q->elements[q->head].len = 0;

  q->head = (q->head + 1) % q->capacity;
  q->count--;

  pthread_cond_signal(&q->not_full);

  // Read from eventfd to clear the notification
  if (q->eventfd >= 0) {
    uint64_t value;
    ssize_t result = read(q->eventfd, &value, sizeof(value));
    if (result != sizeof(value) && errno != EAGAIN && errno != EWOULDBLOCK) {
      log_debug("Failed to read from eventfd: %s", strerror(errno));
    }
  }

  pthread_mutex_unlock(&q->mutex);
  return 0;
}

int queue_is_empty(queue_t *q) {
  if (!q) {
    return -1;
  }

  pthread_mutex_lock(&q->mutex);
  int empty = (q->count == 0) ? 1 : 0;
  pthread_mutex_unlock(&q->mutex);

  return empty;
}

ssize_t queue_size(queue_t *q) {
  if (!q) {
    return -1;
  }

  pthread_mutex_lock(&q->mutex);
  ssize_t size = q->count;
  pthread_mutex_unlock(&q->mutex);

  return size;
}

void queue_close(queue_t *q) {
  if (!q) {
    return;
  }

  pthread_mutex_lock(&q->mutex);
  q->closed = 1;
  pthread_cond_broadcast(&q->not_empty);
  pthread_cond_broadcast(&q->not_full);
  pthread_mutex_unlock(&q->mutex);
}

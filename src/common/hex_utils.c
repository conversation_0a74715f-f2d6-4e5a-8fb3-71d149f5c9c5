#include "common/hex_utils.h"

/**
 * Print a single line of hex dump with ASCII representation (static helper
 * function)
 * @param data Pointer to the data buffer to print
 * @param len Length of the data to print in this line
 * @param offset Offset value to display at the start of the line
 */
static void print_hex_dump_line(const unsigned char *data, int len,
                                int offset) {
  int i;
  int hex_chars_count = 0;
  // Max hex characters for a 16-byte line: (16/2)*4 + (16/2-1) = 32 + 7 = 39
  const int max_hex_display_width = (16 / 2) * 4 + (16 / 2 - 1);

  printf("%04x: ", offset);

  // Print hex in 2-byte groups
  for (i = 0; i < len; i += 2) {
    if (i > 0) {
      printf(" ");
      hex_chars_count++;
    }
    if (i + 1 < len) {
      printf("%02x%02x", data[i], data[i + 1]);
      hex_chars_count += 4;
    } else {
      // Handle odd byte at the end of the data
      printf("%02x  ", data[i]);
      hex_chars_count += 4;
    }
  }

  // Pad with spaces to align ASCII output
  for (i = hex_chars_count; i < max_hex_display_width; i++) {
    printf(" ");
  }

  printf("  "); // Separator

  // Print ASCII representation
  for (i = 0; i < len; i++) {
    if (isprint(data[i]))
      printf("%c", data[i]);
    else
      printf(".");
  }
  printf("\n");
}

/**
 * Print data in hexdump format with ASCII representation
 * @param data Pointer to the data buffer to print
 * @param len Length of the data buffer in bytes
 */
void print_hexdump(const unsigned char *data, int len) {
  int len_rem = len;
  int line_width = 16; // Bytes per line
  int line_len;
  int offset = 0;
  const unsigned char *ch = data;

  if (len <= 0)
    return;

  printf("Hexdump (len %d):\n", len);

  if (len <= line_width) {
    print_hex_dump_line(ch, len, offset);
    return;
  }

  // Print line by line for data larger than line_width
  for (;;) {
    line_len = line_width;
    if (line_len > len_rem)
      line_len = len_rem;

    print_hex_dump_line(ch, line_len, offset);

    len_rem -= line_len;
    ch += line_len;
    offset += line_len;

    if (len_rem <= 0) {
      break;
    }
  }
}

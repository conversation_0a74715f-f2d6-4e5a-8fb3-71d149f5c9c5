#include "common/protocol.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

padded_message_t *create_padded_message(uint32_t client_fd, message_cmd_t cmd,
                                        const uint8_t *data, uint32_t data_len,
                                        int padding_size) {
  padded_message_t *pmsg = malloc(sizeof(padded_message_t));
  if (!pmsg) {
    log_error("Failed to allocate memory for padded message");
    return NULL;
  }

  size_t msg_size = sizeof(message_t) + data_len;
  pmsg->total_size = padding_size + msg_size + padding_size;

  pmsg->buffer = calloc(1, pmsg->total_size);
  if (!pmsg->buffer) {
    log_error("Failed to allocate memory for message buffer");
    free(pmsg);
    return NULL;
  }

  pmsg->padding_start = pmsg->buffer;
  pmsg->msg = (message_t *)(pmsg->buffer + padding_size);
  pmsg->padding_end = pmsg->buffer + padding_size + msg_size;

  // Set magic string for fast identification
  memcpy(pmsg->msg->magic, SITP_MAGIC, SITP_MAGIC_LEN);
  pmsg->msg->client_fd = client_fd;
  pmsg->msg->cmd = cmd;
  pmsg->msg->data_len = data_len;

  if (data && data_len > 0) {
    memcpy(pmsg->msg->data, data, data_len);
  }

  log_debug(
      "Created padded message: fd=%u, cmd=%d, data_len=%u, total_size=%zu",
      client_fd, cmd, data_len, pmsg->total_size);

  return pmsg;
}

message_t *extract_message_from_padded(const uint8_t *buffer, size_t buffer_len,
                                       int padding_size) {
  if (!buffer || buffer_len < (size_t)(2 * padding_size + sizeof(message_t))) {
    log_error("Invalid buffer for message extraction");
    return NULL;
  }

  message_t *src_msg = (message_t *)(buffer + padding_size);
  size_t expected_size =
      padding_size + sizeof(message_t) + src_msg->data_len + padding_size;

  if (buffer_len < expected_size) {
    log_error("Buffer too small for message: expected=%zu, actual=%zu",
              expected_size, buffer_len);
    return NULL;
  }

  size_t msg_size = sizeof(message_t) + src_msg->data_len;
  message_t *msg = malloc(msg_size);
  if (!msg) {
    log_error("Failed to allocate memory for extracted message");
    return NULL;
  }

  memcpy(msg, src_msg, msg_size);

  log_debug("Extracted message: fd=%u, cmd=%d, data_len=%u", msg->client_fd,
            msg->cmd, msg->data_len);

  return msg;
}

void free_padded_message(padded_message_t *pmsg) {
  if (pmsg) {
    if (pmsg->buffer) {
      free(pmsg->buffer);
    }
    free(pmsg);
  }
}

size_t get_padded_message_size(uint32_t data_len, int padding_size) {
  return padding_size + sizeof(message_t) + data_len + padding_size;
}

int validate_padded_message(const uint8_t *buffer, size_t buffer_len,
                            int padding_size) {
  if (!buffer || buffer_len < (size_t)(2 * padding_size + sizeof(message_t))) {
    return 0;
  }

  message_t *msg = (message_t *)(buffer + padding_size);

  // Fast magic string check - this should be the first validation
  if (memcmp(msg->magic, SITP_MAGIC, SITP_MAGIC_LEN) != 0) {
    return 0;
  }

  size_t expected_size = get_padded_message_size(msg->data_len, padding_size);

  if (buffer_len < expected_size) {
    return 0;
  }

  for (int i = 0; i < padding_size; i++) {
    if (buffer[i] != 0 ||
        buffer[padding_size + sizeof(message_t) + msg->data_len + i] != 0) {
      return 0;
    }
  }

  return 1;
}

int is_sitp_message(const uint8_t *buffer, size_t buffer_len, int padding_size) {
  // Fast check - only verify minimum size and magic string
  if (!buffer || buffer_len < (size_t)(padding_size + SITP_MAGIC_LEN)) {
    return 0;
  }

  message_t *msg = (message_t *)(buffer + padding_size);
  return (memcmp(msg->magic, SITP_MAGIC, SITP_MAGIC_LEN) == 0);
}

// Message buffer functions for handling fragmented/concatenated messages
message_buffer_t *message_buffer_create(size_t initial_size) {
  if (initial_size == 0) {
    initial_size = 8192;  // Default 8KB
  }
  if (initial_size > MAX_RECV_BUFFER_SIZE) {
    initial_size = MAX_RECV_BUFFER_SIZE;
  }

  message_buffer_t *buf = malloc(sizeof(message_buffer_t));
  if (!buf) {
    log_error("Failed to allocate memory for message buffer");
    return NULL;
  }

  buf->buffer = malloc(initial_size);
  if (!buf->buffer) {
    log_error("Failed to allocate memory for message buffer data");
    free(buf);
    return NULL;
  }

  buf->buffer_size = initial_size;
  buf->data_len = 0;

  if (pthread_mutex_init(&buf->mutex, NULL) != 0) {
    log_error("Failed to initialize message buffer mutex");
    free(buf->buffer);
    free(buf);
    return NULL;
  }

  log_debug("Created message buffer with size %zu", initial_size);
  return buf;
}

void message_buffer_destroy(message_buffer_t *buf) {
  if (buf) {
    pthread_mutex_destroy(&buf->mutex);
    if (buf->buffer) {
      free(buf->buffer);
    }
    free(buf);
  }
}

int message_buffer_append(message_buffer_t *buf, const uint8_t *data, size_t len) {
  if (!buf || !data || len == 0) {
    return -1;
  }

  pthread_mutex_lock(&buf->mutex);

  // Check if we need to expand the buffer
  if (buf->data_len + len > buf->buffer_size) {
    size_t new_size = buf->buffer_size;
    while (new_size < buf->data_len + len && new_size < MAX_RECV_BUFFER_SIZE) {
      new_size *= 2;
    }

    if (new_size > MAX_RECV_BUFFER_SIZE) {
      new_size = MAX_RECV_BUFFER_SIZE;
    }

    if (buf->data_len + len > new_size) {
      log_error("Message buffer overflow: required=%zu, max=%zu",
                buf->data_len + len, new_size);
      pthread_mutex_unlock(&buf->mutex);
      return -1;
    }

    uint8_t *new_buffer = realloc(buf->buffer, new_size);
    if (!new_buffer) {
      log_error("Failed to expand message buffer to size %zu", new_size);
      pthread_mutex_unlock(&buf->mutex);
      return -1;
    }

    buf->buffer = new_buffer;
    buf->buffer_size = new_size;
    log_debug("Expanded message buffer to size %zu", new_size);
  }

  // Append the new data
  memcpy(buf->buffer + buf->data_len, data, len);
  buf->data_len += len;

  pthread_mutex_unlock(&buf->mutex);
  return 0;
}

int message_buffer_process_messages(message_buffer_t *buf, int padding_size,
                                   message_processor_func_t processor, void *arg) {
  if (!buf || !processor) {
    return -1;
  }

  pthread_mutex_lock(&buf->mutex);

  size_t processed = 0;
  size_t offset = 0;

  while (offset < buf->data_len) {
    // Check if we have enough data for minimum message size
    size_t min_msg_size = 2 * padding_size + sizeof(message_t);
    if (buf->data_len - offset < min_msg_size) {
      // Not enough data for a complete message header
      break;
    }

    // Check if this looks like a valid SITP message
    if (!is_sitp_message(buf->buffer + offset, buf->data_len - offset, padding_size)) {
      // Try to find the next potential message start by looking for magic string
      size_t search_start = offset + 1;
      int found = 0;

      for (size_t i = search_start; i <= buf->data_len - (padding_size + SITP_MAGIC_LEN); i++) {
        if (is_sitp_message(buf->buffer + i, buf->data_len - i, padding_size)) {
          log_warn("Found potential message start at offset %zu, skipping %zu bytes",
                   i, i - offset);
          offset = i;
          found = 1;
          break;
        }
      }

      if (!found) {
        // No valid message found, discard all remaining data
        log_warn("No valid SITP message found, discarding %zu bytes",
                 buf->data_len - offset);
        offset = buf->data_len;
        break;
      }
    }

    // Extract message header to get the expected size
    message_t *msg_header = (message_t *)(buf->buffer + offset + padding_size);
    size_t expected_msg_size = get_padded_message_size(msg_header->data_len, padding_size);

    // Check if we have the complete message
    if (buf->data_len - offset < expected_msg_size) {
      // Incomplete message, wait for more data
      break;
    }

    // Validate the complete message
    if (!validate_padded_message(buf->buffer + offset, expected_msg_size, padding_size)) {
      log_warn("Invalid complete message at offset %zu, skipping", offset);
      offset++;
      continue;
    }

    // Process the complete message
    processor(buf->buffer + offset, expected_msg_size, arg);

    offset += expected_msg_size;
    processed++;
  }

  // Move remaining data to the beginning of the buffer
  if (offset > 0 && offset < buf->data_len) {
    memmove(buf->buffer, buf->buffer + offset, buf->data_len - offset);
    buf->data_len -= offset;
    log_debug("Moved %zu bytes to buffer start, %zu bytes remaining",
              buf->data_len, buf->data_len);
  } else if (offset >= buf->data_len) {
    // All data processed
    buf->data_len = 0;
  }

  pthread_mutex_unlock(&buf->mutex);

  log_debug("Processed %zu complete messages", processed);
  return processed;
}

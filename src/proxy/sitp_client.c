#include "proxy/sitp_client.h"
#include "common/protocol.h"
#include "log.h"
#include "sitp_lib.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

static void sitp_recv_callback(void *arg, uint8_t *buffer, size_t len);
static void *sitp_thread_func(void *arg);
static void process_complete_message(const uint8_t *message_data, size_t message_len, void *arg);

int proxy_sitp_client_init(proxy_sitp_client_t *client, proxy_config_t *config,
                           proxy_pipes_t *pipes) {
  memset(client, 0, sizeof(proxy_sitp_client_t));
  client->config = config;
  client->pipes = pipes;
  client->running = 0;

  // Create message buffer for handling fragmented/concatenated messages
  client->recv_buffer = message_buffer_create(8192);
  if (!client->recv_buffer) {
    log_error("Failed to create message buffer");
    return -1;
  }

  client->sitp_handle =
      sitp_lib_add(config->sitp_interface, config->sitp_mtu,
                   config->sitp_protocol, config->sitp_local_board_id,
                   config->sitp_remote_board_id, config->sitp_local_port,
                   config->sitp_remote_port, sitp_recv_callback, client);

  if (!client->sitp_handle) {
    log_error("Failed to add SITP interface");
    message_buffer_destroy(client->recv_buffer);
    return -1;
  }

  log_info("Proxy SITP client initialized:");
  log_info("  Interface    : %s", config->sitp_interface);
  log_info("  Protocol     : 0x%04x", config->sitp_protocol);
  log_info("  Local ID     : 0x%04x", config->sitp_local_board_id);
  log_info("  Remote ID    : 0x%04x", config->sitp_remote_board_id);
  log_info("  Local Port   : %d", config->sitp_local_port);
  log_info("  Remote Port  : %d", config->sitp_remote_port);
  log_info("  MTU Size     : %d", config->sitp_mtu);

  return 0;
}

int proxy_sitp_client_start(proxy_sitp_client_t *client) {
  client->running = 1;

  if (pthread_create(&client->sitp_thread, NULL, sitp_thread_func, client) !=
      0) {
    log_error("Failed to create SITP thread");
    client->running = 0;
    return -1;
  }

  log_info("Proxy SITP client started");
  return 0;
}

void proxy_sitp_client_stop(proxy_sitp_client_t *client) {
  if (client->running) {
    client->running = 0;
    pthread_cancel(client->sitp_thread);
    pthread_join(client->sitp_thread, NULL);
    log_info("Proxy SITP client stopped");
  }
}

void proxy_sitp_client_cleanup(proxy_sitp_client_t *client) {
  proxy_sitp_client_stop(client);
  if (client->sitp_handle) {
    // SITP lib cleanup is handled by sitp_lib_stop()
    client->sitp_handle = NULL;
  }

  if (client->recv_buffer) {
    message_buffer_destroy(client->recv_buffer);
    client->recv_buffer = NULL;
  }
}

int proxy_sitp_client_send(proxy_sitp_client_t *client, const uint8_t *data,
                           size_t len) {
  if (!client->sitp_handle || !client->running) {
    return -1;
  }

  // Add delay if configured (same as bridge implementation)
  if (client->config->sitp_send_delay_ns > 0) {
    struct timespec delay = {
        .tv_sec = client->config->sitp_send_delay_ns / 1000000000L,
        .tv_nsec = client->config->sitp_send_delay_ns % 1000000000L};
    nanosleep(&delay, NULL);
  }

  int result = sitp_lib_send(client->sitp_handle, (uint8_t *)data, len);
  if (result == 0 && client->config->verbose) {
    log_debug("Successfully sent %zu bytes to SITP", len);
  } else if (result == -1 && client->config->verbose) {
    log_debug("Failed to send %zu bytes to SITP", len);
  }

  return result;
}

static void sitp_recv_callback(void *arg, uint8_t *buffer, size_t len) {
  proxy_sitp_client_t *client = (proxy_sitp_client_t *)arg;

  if (client->config->verbose) {
    log_debug("Received %zu bytes from SITP", len);
  }

  // Append received data to buffer
  if (message_buffer_append(client->recv_buffer, buffer, len) < 0) {
    log_error("Failed to append data to message buffer");
    return;
  }

  // Process complete messages from buffer
  int processed = message_buffer_process_messages(client->recv_buffer,
                                                  client->config->padding_size,
                                                  process_complete_message,
                                                  client);

  if (processed < 0) {
    log_error("Failed to process messages from buffer");
  } else if (processed > 0 && client->config->verbose) {
    log_debug("Processed %d complete messages", processed);
  }
}

static void process_complete_message(const uint8_t *message_data, size_t message_len, void *arg) {
  proxy_sitp_client_t *client = (proxy_sitp_client_t *)arg;

  // Validate the complete message one more time
  if (!validate_padded_message(message_data, message_len, client->config->padding_size)) {
    log_warn("Invalid complete message in processor");
    return;
  }

  // Write the complete message to pipe for processing
  if (proxy_pipes_write_sitp_to_tcp(client->pipes, message_data, message_len) < 0) {
    log_error("Failed to write complete SITP message to pipe");
  } else if (client->config->verbose) {
    log_debug("Forwarded complete %zu bytes message from SITP to pipe", message_len);
  }
}

static void *sitp_thread_func(void *arg) {
  proxy_sitp_client_t *client = (proxy_sitp_client_t *)arg;

  log_info("SITP thread started");
  sitp_lib_start();
  log_info("SITP thread stopped");

  return NULL;
}

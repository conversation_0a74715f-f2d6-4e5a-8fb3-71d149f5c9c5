#include "proxy/pipes.h"
#include "log.h"
#include <stdlib.h>

// Forward declarations for callbacks implemented in data_flow.c
extern void sitp_to_tcp_pipe_callback(int fd, short event, void *arg);
extern void tcp_to_sitp_pipe_callback(int fd, short event, void *arg);

int proxy_pipes_init(proxy_pipes_t *pipes, struct event_base *base, int queue_capacity) {
  pipes->base = base;
  pipes->sitp_to_tcp_queue = NULL;
  pipes->tcp_to_sitp_queue = NULL;

  // Create SITP to TCP queue
  pipes->sitp_to_tcp_queue = queue_create(queue_capacity, base);
  if (!pipes->sitp_to_tcp_queue) {
    log_error("Failed to create sitp_to_tcp queue");
    return -1;
  }

  // Create TCP to SITP queue
  pipes->tcp_to_sitp_queue = queue_create(queue_capacity, base);
  if (!pipes->tcp_to_sitp_queue) {
    log_error("Failed to create tcp_to_sitp queue");
    queue_destroy(pipes->sitp_to_tcp_queue);
    pipes->sitp_to_tcp_queue = NULL;
    return -1;
  }

  // Set callbacks for the queues
  if (queue_set_callback(pipes->sitp_to_tcp_queue, sitp_to_tcp_pipe_callback, pipes) < 0) {
    log_error("Failed to set sitp_to_tcp queue callback");
    queue_destroy(pipes->tcp_to_sitp_queue);
    queue_destroy(pipes->sitp_to_tcp_queue);
    return -1;
  }

  if (queue_set_callback(pipes->tcp_to_sitp_queue, tcp_to_sitp_pipe_callback, pipes) < 0) {
    log_error("Failed to set tcp_to_sitp queue callback");
    queue_destroy(pipes->tcp_to_sitp_queue);
    queue_destroy(pipes->sitp_to_tcp_queue);
    return -1;
  }

  log_info("Proxy pipes initialized successfully");
  return 0;
}

void proxy_pipes_cleanup(proxy_pipes_t *pipes) {
  if (!pipes) {
    return;
  }

  if (pipes->sitp_to_tcp_queue) {
    queue_destroy(pipes->sitp_to_tcp_queue);
    pipes->sitp_to_tcp_queue = NULL;
  }

  if (pipes->tcp_to_sitp_queue) {
    queue_destroy(pipes->tcp_to_sitp_queue);
    pipes->tcp_to_sitp_queue = NULL;
  }

  log_info("Proxy pipes cleaned up");
}

int proxy_pipes_write_sitp_to_tcp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len) {
  if (!pipes || !data || len == 0) {
    return -1;
  }

  return queue_enqueue(pipes->sitp_to_tcp_queue, data, len);
}

int proxy_pipes_write_tcp_to_sitp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len) {
  if (!pipes || !data || len == 0) {
    return -1;
  }

  return queue_enqueue(pipes->tcp_to_sitp_queue, data, len);
}

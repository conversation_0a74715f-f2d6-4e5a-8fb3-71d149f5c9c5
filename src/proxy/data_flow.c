#include "proxy/data_flow.h"
#include "common/hex_utils.h"
#include "common/protocol.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

// Forward declarations
static int handle_sitp_to_tcp_message(proxy_data_flow_t *flow, message_t *msg);
static int process_sitp_data_command(proxy_data_flow_t *flow, message_t *msg);
static int process_sitp_disconnect_command(proxy_data_flow_t *flow,
                                           message_t *msg);

static void send_disconnect_notification(proxy_data_flow_t *flow,
                                         uint32_t connection_id);

static proxy_data_flow_t *global_data_flow = NULL;

int proxy_data_flow_init(proxy_data_flow_t *flow,
                         tcp_client_manager_t *tcp_manager,
                         proxy_sitp_client_t *sitp_client, proxy_pipes_t *pipes,
                         proxy_config_t *config) {
  flow->tcp_manager = tcp_manager;
  flow->sitp_client = sitp_client;
  flow->pipes = pipes;
  flow->config = config;

  global_data_flow = flow;

  log_info("Proxy data flow initialized");
  return 0;
}

void proxy_data_flow_cleanup(proxy_data_flow_t *flow) {
  global_data_flow = NULL;
}

// Pipe callback implementations
void sitp_to_tcp_pipe_callback(int fd, short event, void *arg) {
  proxy_pipes_t *pipes = (proxy_pipes_t *)arg;
  if (!global_data_flow) {
    log_error("Data flow not initialized");
    return;
  }

  uint8_t *buffer = NULL;
  size_t len = 0;
  message_t *msg = NULL;

  // Process all available messages in the queue
  while (queue_dequeue(pipes->sitp_to_tcp_queue, &buffer, &len) == 0) {
    log_debug("Processing %zu bytes from SITP queue", len);

    // Message validation already performed in sitp_recv_callback
    // Extract message directly from the validated padded buffer
    msg = extract_message_from_padded(buffer, len, global_data_flow->config->padding_size);
    if (!msg) {
      log_error("Failed to extract message from padded buffer");
      free(buffer);
      continue;
    }

    if (handle_sitp_to_tcp_message(global_data_flow, msg) < 0) {
      log_error("Failed to handle SITP to TCP message");
    }

    free(msg);
    free(buffer);
  }
}

void tcp_to_sitp_pipe_callback(int fd, short event, void *arg) {
  proxy_pipes_t *pipes = (proxy_pipes_t *)arg;
  if (!global_data_flow) {
    log_error("Data flow not initialized");
    return;
  }

  uint8_t *buffer = NULL;
  size_t len = 0;

  // Process all available messages in the queue
  while (queue_dequeue(pipes->tcp_to_sitp_queue, &buffer, &len) == 0) {
    // Validate simple message structure
    if (len < sizeof(message_t)) {
      log_warn("Invalid message size received from TCP queue: %zu bytes", len);
      free(buffer);
      continue;
    }

    // Cast buffer directly to message_t
    message_t *msg = (message_t *)buffer;
    size_t expected_size = sizeof(message_t) + msg->data_len;

    if (len < expected_size) {
      log_warn("Incomplete message received from TCP queue: expected=%zu, actual=%zu",
               expected_size, len);
      free(buffer);
      continue;
    }

    // Create padded message for SITP transmission
    padded_message_t *pmsg = create_padded_message(msg->client_fd, msg->cmd,
                                                   msg->data, msg->data_len,
                                                   global_data_flow->config->padding_size);
    if (pmsg) {
      if (proxy_sitp_client_send(global_data_flow->sitp_client, pmsg->buffer, pmsg->total_size) < 0) {
        log_error("Failed to send data to SITP");
      }
      free_padded_message(pmsg);
    }

    free(buffer);
  }
}

// Handle SITP to TCP message routing
static int handle_sitp_to_tcp_message(proxy_data_flow_t *flow, message_t *msg) {
  if (!flow || !msg) {
    log_error("Invalid parameters for SITP to TCP message handling");
    return -1;
  }

  if (flow->config->verbose) {
    log_debug("Processing SITP message: client_fd=%u, cmd=%d, data_len=%u",
              msg->client_fd, msg->cmd, msg->data_len);
    if (msg->data_len > 0) {
      print_hexdump(msg->data, msg->data_len);
    }
  }

  switch (msg->cmd) {
  case CMD_DATA:
    return process_sitp_data_command(flow, msg);
  case CMD_DISCONNECT:
    return process_sitp_disconnect_command(flow, msg);
  default:
    log_warn("Unknown command received from SITP: %d", msg->cmd);
    return -1;
  }
}

// Process SITP data command
static int process_sitp_data_command(proxy_data_flow_t *flow, message_t *msg) {
  log_debug("Forwarding %u bytes from SITP to TCP server for client %u",
            msg->data_len, msg->client_fd);

  // Check if TCP connection exists for this client_fd
  tcp_connection_t *conn =
      tcp_client_get_connection(flow->tcp_manager, msg->client_fd);
  if (!conn) {
    // Connection doesn't exist, create new one and send data
    log_info(
        "Received data for non-existent connection %u, creating new connection",
        msg->client_fd);
    conn = tcp_client_create_connection(flow->tcp_manager, msg->client_fd);
    if (!conn) {
      log_error("Failed to create TCP connection for client %u",
                msg->client_fd);
      // Send disconnect notification back to bridge since connection creation
      // failed
      send_disconnect_notification(flow, msg->client_fd);
      return -1;
    }
  }

  // Send data to TCP server (will be buffered if connection not yet
  // established)
  if (tcp_client_send_data(conn, msg->data, msg->data_len) < 0) {
    log_error("Failed to send data to TCP server for client %u",
              msg->client_fd);
    return -1;
  }

  return 0;
}

// Process SITP disconnect command
static int process_sitp_disconnect_command(proxy_data_flow_t *flow,
                                           message_t *msg) {
  log_info("Received disconnect notification from SITP for client %u",
           msg->client_fd);

  // Close TCP connection
  tcp_client_close_connection(flow->tcp_manager, msg->client_fd);
  log_info("Closed TCP connection for client %u", msg->client_fd);

  return 0;
}



static void send_disconnect_notification(proxy_data_flow_t *flow,
                                         uint32_t connection_id) {
  // Create simple disconnect message
  message_t *msg = malloc(sizeof(message_t));
  if (msg) {
    msg->client_fd = connection_id;
    msg->cmd = CMD_DISCONNECT;
    msg->data_len = 0;

    if (proxy_pipes_write_tcp_to_sitp(flow->pipes, (uint8_t*)msg, sizeof(message_t)) < 0) {
      log_error("Failed to send disconnect notification for connection %u", connection_id);
    }
    free(msg);
  }
}

// Function to be called from tcp_client.c when TCP connection receives data
void proxy_tcp_data_received(uint32_t connection_id, const uint8_t *data,
                             size_t len) {
  if (!global_data_flow) {
    return;
  }

  // Create simple message structure, without padding
  message_t *msg = malloc(sizeof(message_t) + len);
  if (!msg) {
    log_error("Failed to allocate memory for message");
    return;
  }

  msg->client_fd = connection_id;
  msg->cmd = CMD_DATA;
  msg->data_len = len;
  memcpy(msg->data, data, len);

  // Write message directly to pipe
  size_t msg_size = sizeof(message_t) + len;
  if (proxy_pipes_write_tcp_to_sitp(global_data_flow->pipes, (uint8_t*)msg, msg_size) < 0) {
    log_error("Failed to forward TCP data to SITP for connection %u", connection_id);
  }

  free(msg);
}

// Function to be called from tcp_client.c when TCP connection is closed
void proxy_tcp_connection_closed(uint32_t connection_id) {
  if (!global_data_flow) {
    return;
  }

  send_disconnect_notification(global_data_flow, connection_id);
}

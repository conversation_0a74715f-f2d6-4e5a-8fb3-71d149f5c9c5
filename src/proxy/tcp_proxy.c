#include "log.h"
#include "proxy/config.h"
#include "proxy/data_flow.h"
#include "proxy/logger.h"
#include "proxy/pipes.h"
#include "proxy/sitp_client.h"
#include "proxy/tcp_client.h"
#include <event2/event.h>
#include <getopt.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

typedef struct proxy_context {
  struct event_base *base;
  proxy_config_t config;
  proxy_pipes_t pipes;
  tcp_client_manager_t tcp_manager;
  proxy_sitp_client_t sitp_client;
  proxy_data_flow_t data_flow;
  int initialized_components;
} proxy_context_t;

#define COMP_CONFIG (1 << 0)
#define COMP_LOGGER (1 << 1)
#define COMP_BASE (1 << 2)
#define COMP_PIPES (1 << 3)
#define COMP_TCP_MANAGER (1 << 4)
#define COMP_SITP_CLIENT (1 << 5)
#define COMP_DATA_FLOW (1 << 6)

static volatile int running = 1;
static proxy_context_t *global_context = NULL;

static void signal_handler(int sig) {
  log_info("Received signal %d, shutting down...", sig);
  running = 0;
  if (global_context && global_context->base) {
    event_base_loopbreak(global_context->base);
  }
  if (global_context && global_context->sitp_client.running) {
    pthread_cancel(global_context->sitp_client.sitp_thread);
    global_context->sitp_client.running = 0;
  }
}

static void print_usage(const char *program_name) {
  printf("Usage: %s [OPTIONS]\n", program_name);
  printf("Options:\n");
  printf(
      "  -c <config_file>  Specify configuration file (default: proxy.conf)\n");
  printf("  -v                Enable verbose mode (hex dump data)\n");
  printf("  -h                Show this help message\n");
  printf("  -V                Show version information\n");
}

static void print_version(void) {
  printf("SITP-TCP Proxy v1.0.0\n");
  printf("Built on %s %s\n", __DATE__, __TIME__);
}

static void proxy_context_cleanup(proxy_context_t *ctx) {
  if (!ctx)
    return;

  if (ctx->initialized_components & COMP_DATA_FLOW) {
    proxy_data_flow_cleanup(&ctx->data_flow);
  }

  if (ctx->initialized_components & COMP_SITP_CLIENT) {
    proxy_sitp_client_stop(&ctx->sitp_client);
    proxy_sitp_client_cleanup(&ctx->sitp_client);
  }

  if (ctx->initialized_components & COMP_TCP_MANAGER) {
    tcp_client_manager_cleanup(&ctx->tcp_manager);
  }

  if (ctx->initialized_components & COMP_PIPES) {
    proxy_pipes_cleanup(&ctx->pipes);
  }

  if (ctx->initialized_components & COMP_BASE) {
    event_base_free(ctx->base);
  }

  if (ctx->initialized_components & COMP_LOGGER) {
    proxy_logger_cleanup();
  }

  if (ctx->initialized_components & COMP_CONFIG) {
    proxy_config_free(&ctx->config);
  }

  ctx->initialized_components = 0;
}

static int proxy_context_init(proxy_context_t *ctx, const char *config_file) {
  memset(ctx, 0, sizeof(*ctx));

  // Load configuration
  if (proxy_config_load(config_file, &ctx->config) < 0) {
    fprintf(stderr, "Failed to load configuration from %s\n", config_file);
    return -1;
  }
  ctx->initialized_components |= COMP_CONFIG;

  // Initialize logger
  if (proxy_logger_init(&ctx->config) < 0) {
    fprintf(stderr, "Failed to initialize logger\n");
    return -1;
  }
  ctx->initialized_components |= COMP_LOGGER;

  log_info("Starting SITP-TCP Proxy...");
  log_info("Configuration file: %s", config_file);

  // Create event base
  ctx->base = event_base_new();
  if (!ctx->base) {
    log_error("Failed to create event base");
    return -1;
  }
  ctx->initialized_components |= COMP_BASE;

  // Initialize pipes
  if (proxy_pipes_init(&ctx->pipes, ctx->base, ctx->config.queue_capacity) < 0) {
    log_error("Failed to initialize pipes");
    return -1;
  }
  ctx->initialized_components |= COMP_PIPES;

  // Initialize TCP client manager
  if (tcp_client_manager_init(&ctx->tcp_manager, ctx->base, &ctx->config,
                              &ctx->pipes) < 0) {
    log_error("Failed to initialize TCP client manager");
    return -1;
  }
  ctx->initialized_components |= COMP_TCP_MANAGER;

  // Initialize SITP client
  if (proxy_sitp_client_init(&ctx->sitp_client, &ctx->config, &ctx->pipes) <
      0) {
    log_error("Failed to initialize SITP client");
    return -1;
  }
  ctx->initialized_components |= COMP_SITP_CLIENT;

  // Initialize data flow
  if (proxy_data_flow_init(&ctx->data_flow, &ctx->tcp_manager,
                           &ctx->sitp_client, &ctx->pipes, &ctx->config) < 0) {
    log_error("Failed to initialize data flow");
    return -1;
  }
  ctx->initialized_components |= COMP_DATA_FLOW;

  return 0;
}

static int proxy_start_services(proxy_context_t *ctx) {
  if (proxy_sitp_client_start(&ctx->sitp_client) < 0) {
    log_error("Failed to start SITP client");
    return -1;
  }

  log_info("Proxy started successfully");
  log_info("Target server: %s:%d", ctx->config.target_host,
           ctx->config.target_port);
  log_info("SITP client connected to %s (protocol=0x%04x)",
           ctx->config.sitp_interface, ctx->config.sitp_protocol);

  return 0;
}

static int parse_command_line(int argc, char *argv[], const char **config_file,
                              int *verbose_override) {
  int opt;

  while ((opt = getopt(argc, argv, "c:hvV")) != -1) {
    switch (opt) {
    case 'c':
      *config_file = optarg;
      break;
    case 'v':
      *verbose_override = 1;
      break;
    case 'h':
      print_usage(argv[0]);
      return 1;
    case 'V':
      print_version();
      return 1;
    default:
      print_usage(argv[0]);
      return -1;
    }
  }

  return 0;
}

int main(int argc, char *argv[]) {
  const char *config_file = "proxy.conf";
  proxy_context_t context;
  int verbose_override = 0;
  int ret;

  // Parse command line arguments
  ret = parse_command_line(argc, argv, &config_file, &verbose_override);
  if (ret != 0) {
    return (ret > 0) ? 0 : 1; // Return 0 for help/version, 1 for errors
  }

  // Initialize proxy context
  if (proxy_context_init(&context, config_file) < 0) {
    proxy_context_cleanup(&context);
    return 1;
  }

  // Override verbose setting if specified on command line
  if (verbose_override) {
    context.config.verbose = 1;
    log_info("Verbose mode enabled via command line");
  }

  // Set up signal handlers
  global_context = &context;
  signal(SIGINT, signal_handler);
  signal(SIGTERM, signal_handler);
  signal(SIGPIPE, SIG_IGN);

  // Start services
  if (proxy_start_services(&context) < 0) {
    global_context = NULL;
    proxy_context_cleanup(&context);
    return 1;
  }

  // Run main event loop
  event_base_dispatch(context.base);

  log_info("Shutting down proxy...");

  // Clean shutdown
  global_context = NULL;
  proxy_context_cleanup(&context);

  log_info("Proxy shut down complete");
  return 0;
}

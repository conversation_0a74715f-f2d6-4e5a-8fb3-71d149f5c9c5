/* ========================================================================
 * SITP Library Implementation
 * Low-level network communication interfaces for industrial control systems
 * ======================================================================== */

#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif

/* ========================================================================
 * INCLUDES
 * ======================================================================== */
#include "sitp_lib.h"
#include <errno.h>
#include <event2/buffer.h>
#include <event2/event.h>
#include <fcntl.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/select.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

/* ========================================================================
 * CONSTANTS AND MACROS
 * ======================================================================== */
#define SITP_PIPE_A "/tmp/sitp_pipe_a"
#define SITP_PIPE_B "/tmp/sitp_pipe_b"
#define MAX_INTERFACES 16
#define BUFFER_SIZE 8192

/* ========================================================================
 * TYPE DEFINITIONS
 * ======================================================================== */
typedef struct sitp_interface {
  char eth_dev[32];   /* Network interface name */
  int mtu;            /* Maximum transmission unit */
  uint16_t protocol;  /* Protocol number for FPGA filtering */
  uint16_t local_id;  /* Local board ID (e.g., M300-FF10) */
  uint16_t remote_id; /* Remote board ID (e.g., M300-FF12) */
  uint16_t local_port;
  uint16_t remote_port;
  sitp_lib_recv_data_func pfn_recv; /* Data reception callback */
  void *arg;                        /* User argument for callback */
  int write_fd;                     /* Named pipe write file descriptor */
  int read_fd;                      /* Named pipe read file descriptor */
  int valid;                        /* Interface validity flag */
  struct event *read_event;         /* libevent read event handler */
} sitp_interface_t;

/* ========================================================================
 * GLOBAL VARIABLES
 * ======================================================================== */
static sitp_interface_t interfaces[MAX_INTERFACES]; /* Interface pool */
static int interface_count = 0;                     /* Active interface count */
static pthread_mutex_t interfaces_mutex =
    PTHREAD_MUTEX_INITIALIZER;               /* Thread safety */
static int running = 0;                      /* Event loop state */
static struct event_base *event_base = NULL; /* libevent base */
static int sitp_initialized = 0;             /* Initialization flag */

/* ========================================================================
 * FORWARD DECLARATIONS
 * ======================================================================== */

static void sitp_init_signals(void);
static void sitp_signal_handler(int sig);
static int create_named_pipes(void);
static void read_callback(evutil_socket_t fd, short events, void *arg);
static void reconnect_callback(evutil_socket_t fd, short events, void *arg);
static void free_reconnect_event(evutil_socket_t fd, short events, void *arg);

/* ========================================================================
 * SIGNAL HANDLING
 * ======================================================================== */
static void sitp_signal_handler(int sig) {
  /* Silently ignore SIGPIPE to prevent process termination */
}

static void sitp_init_signals(void) {
  if (sitp_initialized)
    return;

  /* Configure SIGPIPE handling to prevent process termination on broken pipes
   */
  struct sigaction sa;
  sa.sa_handler = sitp_signal_handler;
  sigemptyset(&sa.sa_mask);
  sa.sa_flags = 0;
  sigaction(SIGPIPE, &sa, NULL);

  sitp_initialized = 1;
}

/* ========================================================================
 * PIPE MANAGEMENT
 * ======================================================================== */
static int create_named_pipes(void) {
  struct stat st;

  if (stat(SITP_PIPE_A, &st) != 0) {
    if (mkfifo(SITP_PIPE_A, 0666) != 0) {
      perror("mkfifo SITP_PIPE_A");
      return -1;
    }
  }

  if (stat(SITP_PIPE_B, &st) != 0) {
    if (mkfifo(SITP_PIPE_B, 0666) != 0) {
      perror("mkfifo SITP_PIPE_B");
      return -1;
    }
  }

  return 0;
}

/* ========================================================================
 * EVENT HANDLING CALLBACKS
 * ======================================================================== */
static void read_callback(evutil_socket_t fd, short events, void *arg) {
  sitp_interface_t *iface = (sitp_interface_t *)arg;
  uint8_t buffer[BUFFER_SIZE];

  if (!iface->valid) {
    return;
  }

  if (events & EV_READ) {
    ssize_t bytes_read = read(fd, buffer, sizeof(buffer));

    if (bytes_read > 0) {
      /* Forward received data to user callback */
      if (iface->pfn_recv) {
        iface->pfn_recv(iface->arg, buffer, bytes_read);
      }
    } else if (bytes_read == 0) {
      /* Writer closed - cleanup and schedule reconnection */
      if (iface->read_event) {
        event_del(iface->read_event);
        event_free(iface->read_event);
        iface->read_event = NULL;
      }
      close(iface->read_fd);
      iface->read_fd = -1;

      /* Schedule reconnection attempt in 2 seconds */
      if (event_base) {
        struct timeval tv = {2, 0};
        struct event *reconnect_event =
            event_new(event_base, -1, EV_TIMEOUT, reconnect_callback, iface);
        if (reconnect_event) {
          event_add(reconnect_event, &tv);
        }
      }
    } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
      /* Handle other read errors - attempt reconnection */
      if (iface->read_event) {
        event_del(iface->read_event);
        event_free(iface->read_event);
        iface->read_event = NULL;
      }
      close(iface->read_fd);
      iface->read_fd = -1;

      /* Schedule reconnection attempt in 1 second for errors */
      if (event_base) {
        struct timeval tv = {1, 0};
        struct event *reconnect_event =
            event_new(event_base, -1, EV_TIMEOUT, reconnect_callback, iface);
        if (reconnect_event) {
          event_add(reconnect_event, &tv);
        }
      }
    }
  }
}

static void reconnect_callback(evutil_socket_t fd, short events, void *arg) {
  sitp_interface_t *iface = (sitp_interface_t *)arg;

  if (!iface->valid || iface->read_fd >= 0) {
    return;
  }

  /* Determine pipe name based on board ID ordering */
  const char *pipe_name =
      (iface->local_id < iface->remote_id) ? SITP_PIPE_B : SITP_PIPE_A;
  iface->read_fd = open(pipe_name, O_RDONLY | O_NONBLOCK);

  if (iface->read_fd >= 0) {
    /* Reconnection successful - restore read event */
    iface->read_event = event_new(event_base, iface->read_fd,
                                  EV_READ | EV_PERSIST, read_callback, iface);
    if (iface->read_event) {
      event_add(iface->read_event, NULL);
    }
  } else {
    /* Reconnection failed - schedule retry with longer interval */
    if (event_base && iface->valid) {
      struct timeval tv = {3, 0}; /* Increased retry interval */
      struct event *retry_event =
          event_new(event_base, -1, EV_TIMEOUT, reconnect_callback, iface);
      if (retry_event) {
        event_add(retry_event, &tv);
      }
    }
  }
}

static void free_reconnect_event(evutil_socket_t fd, short events, void *arg) {
  struct event *event_to_free = (struct event *)arg;
  event_free(event_to_free);
}

/* ========================================================================
 * PUBLIC API FUNCTIONS
 * ======================================================================== */
void *sitp_lib_add(const char *eth_dev, int mtu, uint16_t protocol,
                   uint16_t local_id, uint16_t remote_id, uint16_t local_port,
                   uint16_t remote_port, sitp_lib_recv_data_func pfn_recv,
                   void *arg) {

  sitp_init_signals();

  pthread_mutex_lock(&interfaces_mutex);

  if (interface_count >= MAX_INTERFACES) {
    pthread_mutex_unlock(&interfaces_mutex);
    return NULL;
  }

  if (create_named_pipes() != 0) {
    pthread_mutex_unlock(&interfaces_mutex);
    return NULL;
  }

  sitp_interface_t *iface = &interfaces[interface_count];
  memset(iface, 0, sizeof(sitp_interface_t));

  /* Initialize interface configuration */
  strncpy(iface->eth_dev, eth_dev, sizeof(iface->eth_dev) - 1);
  iface->mtu = mtu;
  iface->protocol = protocol;
  iface->local_id = local_id;
  iface->remote_id = remote_id;
  iface->local_port = local_port;
  iface->remote_port = remote_port;
  iface->pfn_recv = pfn_recv;
  iface->arg = arg;

  /* Pipe assignment based on board ID ordering prevents deadlocks */
  if (local_id < remote_id) {
    iface->read_fd = open(SITP_PIPE_B, O_RDONLY | O_NONBLOCK);
    usleep(200000); /* Increased delay to allow reader to establish before writer */
    iface->write_fd = open(SITP_PIPE_A, O_WRONLY | O_NONBLOCK);
    if (iface->write_fd < 0 && errno == ENXIO) {
      iface->write_fd = -1; /* Defer until first send attempt */
    }
  } else {
    iface->read_fd = open(SITP_PIPE_A, O_RDONLY | O_NONBLOCK);
    usleep(200000); /* Increased delay */
    iface->write_fd = open(SITP_PIPE_B, O_WRONLY | O_NONBLOCK);
    if (iface->write_fd < 0 && errno == ENXIO) {
      iface->write_fd = -1;
    }
  }

  if (iface->read_fd < 0) {
    if (iface->write_fd >= 0)
      close(iface->write_fd);
    printf("SITP: Failed to open read pipe - read_fd=%d, errno=%d (%s)\n",
           iface->read_fd, errno, strerror(errno));
    printf("SITP: Local ID: 0x%04X, Remote ID: 0x%04X\n", local_id, remote_id);
    pthread_mutex_unlock(&interfaces_mutex);
    return NULL;
  }

  /* Setup event-driven reading if event loop is active */
  if (event_base) {
    iface->read_event = event_new(event_base, iface->read_fd,
                                  EV_READ | EV_PERSIST, read_callback, iface);
    if (iface->read_event) {
      event_add(iface->read_event, NULL);
    }
  }

  iface->valid = 1;
  interface_count++;

  pthread_mutex_unlock(&interfaces_mutex);
  return iface;
}

int sitp_lib_send(void *obj, uint8_t *buffer, size_t len) {
  if (!obj || !buffer || len == 0) {
    return -1;
  }

  sitp_interface_t *iface = (sitp_interface_t *)obj;
  if (!iface->valid) {
    return -1;
  }

  /* Lazy opening of write pipe */
  if (iface->write_fd < 0) {
    const char *pipe_name =
        (iface->local_id < iface->remote_id) ? SITP_PIPE_A : SITP_PIPE_B;
    iface->write_fd = open(pipe_name, O_WRONLY | O_NONBLOCK);

    if (iface->write_fd < 0) {
      if (errno == ENXIO) {
        /* No reader available - simulate success to avoid blocking caller */
        printf("SITP: No reader available for pipe %s (Local: 0x%04X, Remote: 0x%04X)\n",
               pipe_name, iface->local_id, iface->remote_id);
        return 0;
      }
      printf("SITP: Failed to open write pipe %s: %s (Local: 0x%04X, Remote: 0x%04X)\n",
             pipe_name, strerror(errno), iface->local_id, iface->remote_id);
      return -1;
    }
  }

  ssize_t written = write(iface->write_fd, buffer, len);

  if (written < 0) {
    if (errno == EAGAIN || errno == EWOULDBLOCK) {
      return -1; /* Would block - return failure */
    }
    if (errno == EPIPE) {
      /* Reader closed - reset for automatic reconnection */
      close(iface->write_fd);
      iface->write_fd = -1;
      return 0; /* Simulate success */
    }
    return -1;
  }

  /* Force flush to ensure data is immediately available to reader */
  fsync(iface->write_fd);

  /* Add small delay to prevent overwhelming the receiver */
  usleep(1000); /* 1ms delay */

  return 0; /* Success */
}

int sitp_lib_start(void) {
  running = 1;

  event_base = event_base_new();
  if (!event_base) {
    return -1;
  }

  /* Register existing interfaces with event loop */
  pthread_mutex_lock(&interfaces_mutex);
  for (int i = 0; i < interface_count; i++) {
    if (interfaces[i].valid && interfaces[i].read_fd >= 0 &&
        !interfaces[i].read_event) {
      interfaces[i].read_event =
          event_new(event_base, interfaces[i].read_fd, EV_READ | EV_PERSIST,
                    read_callback, &interfaces[i]);
      if (interfaces[i].read_event) {
        event_add(interfaces[i].read_event, NULL);
      }
    }
  }
  pthread_mutex_unlock(&interfaces_mutex);

  /* Main event loop - resilient to temporary failures */
  while (running) {
    int ret = event_base_dispatch(event_base);
    if (ret == 0) {
      usleep(100000); /* No events - brief pause before retry */
      continue;
    } else if (ret == 1) {
      break; /* Clean shutdown requested */
    } else if (ret == -1) {
      usleep(100000); /* Error occurred - brief pause before retry */
      continue;
    }
  }

  /* Cleanup event resources */
  pthread_mutex_lock(&interfaces_mutex);
  for (int i = 0; i < interface_count; i++) {
    if (interfaces[i].read_event) {
      event_free(interfaces[i].read_event);
      interfaces[i].read_event = NULL;
    }
  }
  pthread_mutex_unlock(&interfaces_mutex);

  event_base_free(event_base);
  event_base = NULL;

  return 0;
}

#ifndef COMMON_PROTOCOL_H
#define COMMON_PROTOCOL_H

#include <stdint.h>
#include <sys/types.h>
#include <pthread.h>

// Magic string for SITP message identification
#define SITP_MAGIC "SITP"
#define SITP_MAGIC_LEN 4

// Maximum buffer size for message reassembly
#define MAX_RECV_BUFFER_SIZE (64 * 1024)  // 64KB

typedef enum {
  CMD_DATA = 0,
  CMD_DISCONNECT = 1,
  CMD_PING = 2,
  CMD_PONG = 3
} message_cmd_t;

typedef struct {
  uint8_t magic[SITP_MAGIC_LEN];  // Magic string for fast identification
  uint32_t client_fd;
  message_cmd_t cmd;
  uint32_t data_len;
  uint8_t data[];
} message_t;

typedef struct {
  uint8_t *padding_start;
  message_t *msg;
  uint8_t *padding_end;
  uint8_t *buffer;
  size_t total_size;
} padded_message_t;

// Message buffer for handling fragmented/concatenated messages
typedef struct {
  uint8_t *buffer;
  size_t buffer_size;
  size_t data_len;
  pthread_mutex_t mutex;
} message_buffer_t;

// Function to process complete messages from buffer
typedef void (*message_processor_func_t)(const uint8_t *message_data, size_t message_len, void *arg);

padded_message_t *create_padded_message(uint32_t client_fd, message_cmd_t cmd,
                                        const uint8_t *data, uint32_t data_len,
                                        int padding_size);

message_t *extract_message_from_padded(const uint8_t *buffer, size_t buffer_len,
                                       int padding_size);

void free_padded_message(padded_message_t *pmsg);

size_t get_padded_message_size(uint32_t data_len, int padding_size);

int validate_padded_message(const uint8_t *buffer, size_t buffer_len,
                            int padding_size);

int is_sitp_message(const uint8_t *buffer, size_t buffer_len, int padding_size);

// Message buffer functions for handling fragmented/concatenated messages
message_buffer_t *message_buffer_create(size_t initial_size);
void message_buffer_destroy(message_buffer_t *buf);
int message_buffer_append(message_buffer_t *buf, const uint8_t *data, size_t len);
int message_buffer_process_messages(message_buffer_t *buf, int padding_size,
                                   message_processor_func_t processor, void *arg);

#endif

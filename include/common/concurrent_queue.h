#ifndef CONCURRENT_QUEUE_H
#define CONCURRENT_QUEUE_H

#include <event2/event.h>
#include <pthread.h> // For pthread_mutex_t and pthread_cond_t
#include <stddef.h>  // For size_t
#include <stdint.h>  // For uint8_t

// Structure for an element in the queue
typedef struct {
  uint8_t *buffer;
  size_t len;
} queue_element_t;

// Structure for the concurrent queue with libevent integration
typedef struct {
  queue_element_t *elements; // Array of queue elements
  size_t capacity;           // Maximum number of elements
  size_t count;              // Current number of elements
  size_t head;               // Index of the front element
  size_t tail;               // Index of the next free slot
  pthread_mutex_t mutex;     // Mutex for thread safety
  pthread_cond_t not_empty;  // Condition variable for when queue is not empty
  pthread_cond_t not_full;   // Condition variable for when queue is not full
  int eventfd;               // eventfd for libevent integration
  struct event *event;       // libevent event for this queue
  struct event_base *base;   // event base for this queue
  int closed;                // flag to indicate queue is closed
} queue_t;

typedef void (*queue_callback_t)(int fd, short events, void *arg);

/**
 * @brief Creates a new concurrent queue with libevent integration.
 *
 * @param capacity The maximum number of elements the queue can hold.
 * @param base libevent base for event handling (can be NULL for no libevent integration)
 * @return A pointer to the newly created queue, or NULL on failure.
 */
queue_t *queue_create(size_t capacity, struct event_base *base);

/**
 * @brief Set callback for queue events
 *
 * @param q Pointer to the queue structure
 * @param callback Callback function to be called when data is available
 * @param arg Argument to pass to the callback
 * @return 0 on success, -1 on error
 */
int queue_set_callback(queue_t *q, queue_callback_t callback, void *arg);

/**
 * @brief Destroys a concurrent queue and frees its resources.
 *
 * This function will also free all buffers currently stored in the queue.
 *
 * @param q A pointer to the queue to be destroyed.
 */
void queue_destroy(queue_t *q);

/**
 * @brief Enqueues an element into the concurrent queue.
 *
 * This function makes a copy of the provided buffer and signals libevent if configured.
 *
 * @param q A pointer to the queue.
 * @param buffer A pointer to the buffer to be enqueued.
 * @param len The length of the buffer.
 * @return 0 on success, -1 on failure (e.g., memory allocation error or queue
 * is NULL).
 */
int queue_enqueue(queue_t *q, const uint8_t *buffer, size_t len);

/**
 * @brief Dequeues an element from the concurrent queue (non-blocking).
 *
 * The caller is responsible for freeing the dequeued buffer using free().
 *
 * @param q A pointer to the queue.
 * @param buffer A pointer to a uint8_t* that will be set to the dequeued
 * buffer.
 * @param len A pointer to a size_t that will be set to the length of the
 * dequeued buffer.
 * @return 0 on success, -1 if the queue is NULL, empty, or if an error occurs.
 */
int queue_dequeue(queue_t *q, uint8_t **buffer, size_t *len);

/**
 * @brief Check if queue is empty
 *
 * @param q Pointer to the queue structure
 * @return 1 if empty, 0 if not empty, -1 on error
 */
int queue_is_empty(queue_t *q);

/**
 * @brief Get queue size
 *
 * @param q Pointer to the queue structure
 * @return Number of items in queue, -1 on error
 */
ssize_t queue_size(queue_t *q);

/**
 * @brief Close the queue (no more enqueue operations allowed)
 *
 * @param q Pointer to the queue structure
 */
void queue_close(queue_t *q);

#endif // CONCURRENT_QUEUE_H

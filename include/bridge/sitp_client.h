#ifndef BRIDGE_SITP_CLIENT_H
#define BRIDGE_SITP_CLIENT_H

#include "bridge/config.h"
#include "bridge/pipes.h"
#include "common/protocol.h"
#include <pthread.h>

typedef struct sitp_client {
  void *sitp_handle;
  pthread_t sitp_thread;
  bridge_config_t *config;
  bridge_pipes_t *pipes;
  message_buffer_t *recv_buffer;
  int running;
} sitp_client_t;

int sitp_client_init(sitp_client_t *client, bridge_config_t *config,
                     bridge_pipes_t *pipes);
int sitp_client_start(sitp_client_t *client);
void sitp_client_stop(sitp_client_t *client);
void sitp_client_cleanup(sitp_client_t *client);

#endif

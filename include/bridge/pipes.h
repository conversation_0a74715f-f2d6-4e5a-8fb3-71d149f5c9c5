#ifndef BRIDGE_PIPES_H
#define BRIDGE_PIPES_H

#include "common/concurrent_queue.h"
#include <event2/event.h>

typedef struct bridge_pipes {
  queue_t *tcp_to_sitp_queue;
  queue_t *sitp_to_tcp_queue;
  struct event_base *base;
} bridge_pipes_t;

typedef void (*pipe_data_callback_t)(int fd, short events, void *arg);

int bridge_pipes_init(bridge_pipes_t *pipes, struct event_base *base, int queue_capacity);
void bridge_pipes_cleanup(bridge_pipes_t *pipes);
int bridge_pipes_set_callbacks(bridge_pipes_t *pipes,
                               pipe_data_callback_t tcp_to_sitp_cb,
                               pipe_data_callback_t sitp_to_tcp_cb,
                               void *tcp_to_sitp_arg, void *sitp_to_tcp_arg);

// Functions to write data to queues
int bridge_pipes_write_tcp_to_sitp(bridge_pipes_t *pipes, const uint8_t *data, size_t len);
int bridge_pipes_write_sitp_to_tcp(bridge_pipes_t *pipes, const uint8_t *data, size_t len);

#endif

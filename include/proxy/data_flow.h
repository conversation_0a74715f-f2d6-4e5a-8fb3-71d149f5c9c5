#ifndef PROXY_DATA_FLOW_H
#define PROXY_DATA_FLOW_H

#include "proxy/config.h"
#include "proxy/pipes.h"
#include "proxy/sitp_client.h"
#include "proxy/tcp_client.h"

typedef struct {
  tcp_client_manager_t *tcp_manager;
  proxy_sitp_client_t *sitp_client;
  proxy_pipes_t *pipes;
  proxy_config_t *config;
} proxy_data_flow_t;

int proxy_data_flow_init(proxy_data_flow_t *flow,
                         tcp_client_manager_t *tcp_manager,
                         proxy_sitp_client_t *sitp_client, proxy_pipes_t *pipes,
                         proxy_config_t *config);

void proxy_data_flow_cleanup(proxy_data_flow_t *flow);

// Functions to be called from tcp_client.c
void proxy_tcp_data_received(uint32_t connection_id, const uint8_t *data,
                             size_t len);
void proxy_tcp_connection_closed(uint32_t connection_id);

#endif

#ifndef PROXY_PIPES_H
#define PROXY_PIPES_H

#include "common/concurrent_queue.h"
#include <event2/event.h>

typedef struct {
  queue_t *sitp_to_tcp_queue; // From SITP callback to TCP client
  queue_t *tcp_to_sitp_queue; // From TCP client to SITP send
  struct event_base *base;
} proxy_pipes_t;

int proxy_pipes_init(proxy_pipes_t *pipes, struct event_base *base, int queue_capacity);
void proxy_pipes_cleanup(proxy_pipes_t *pipes);

int proxy_pipes_write_sitp_to_tcp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len);
int proxy_pipes_write_tcp_to_sitp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len);

#endif
